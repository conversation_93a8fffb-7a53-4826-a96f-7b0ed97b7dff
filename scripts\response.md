免实习记录-学生端
接口：/v1/internship/student/exempt/applyRecord
请求类型：GET
入参：planId


```


返回结果data实体：
```json
{
  "data": {
    "avatar": "string",//学生头像
    "createPerson": "string",
    "createTime": 0,
    "dr": 0, //删除标志（0=正常，1=删除）
    "fileUrl": "string", //证明材料文件URL
    "id": 0,
    "planId": 0,
    "reason": "string",//免实习理由
    "reviewId": 0, //审核人ID(老师ID)
    "reviewOpinion": "string", //审核意见
    "reviewPerson": "string", //审核人
    "reviewRole": "string", // 审核人角色
    "status": 0,//状态（0=待审批，1=已通过，2=驳回）
    "studentId": 0,
    "studentName": "string",
    "updatePerson": "string",
    "updateTime": 0
  },
  "resultCode": "string",
  "resultMsg": "string"
}
```