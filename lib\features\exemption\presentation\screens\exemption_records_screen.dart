/// -----
/// exemption_records_screen.dart
///
/// 学生端免实习记录页面
/// 显示当前学生的免实习申请记录，支持查看申请状态和添加新申请
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter_demo/core/theme/app_theme.dart';
import 'package:flutter_demo/core/widgets/custom_app_bar.dart';
import 'package:flutter_demo/core/widgets/course_header_section.dart';
import 'package:flutter_demo/core/widgets/approval_list_item.dart';
import 'package:flutter_demo/core/widgets/page_state_widget.dart';
import 'package:flutter_demo/features/plan/presentation/screens/student_exemption_application_screen.dart';
import '../bloc/exemption_records_bloc.dart';
import '../bloc/exemption_records_event.dart';
import '../bloc/exemption_records_state.dart';
import '../../data/models/exemption_record_model.dart';

/// 学生端免实习记录页面
///
/// 功能包括：
/// - 显示当前学生的免实习申请记录
/// - 支持查看申请状态（待审核、已通过、已拒绝）
/// - 支持添加新的免实习申请
/// - 支持下拉刷新和上拉加载更多
class ExemptionRecordsScreen extends StatelessWidget {
  const ExemptionRecordsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => GetIt.instance<ExemptionRecordsBloc>()
        ..add(const LoadExemptionRecordsEvent()),
      child: const _ExemptionRecordsView(),
    );
  }
}

class _ExemptionRecordsView extends StatelessWidget {
  const _ExemptionRecordsView();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: const CustomAppBar(
        title: '免实习',
        backgroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 课程信息头部
          const CourseHeaderSection(),
          
          // 记录列表区域
          Expanded(
            child: BlocBuilder<ExemptionRecordsBloc, ExemptionRecordsState>(
              builder: (context, state) {
                return _buildContent(context, state);
              },
            ),
          ),
        ],
      ),
      // 右下角浮动按钮
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// 构建主要内容区域
  Widget _buildContent(BuildContext context, ExemptionRecordsState state) {
    if (state is ExemptionRecordsLoadingState) {
      return const PageStateWidget(
        state: PageStateType.initialLoading,
        loadingMessage: '加载免实习记录中...',
      );
    }

    if (state is ExemptionRecordsErrorState) {
      return PageStateWidget(
        state: PageStateType.serverError,
        errorMessage: state.message,
        onRetry: () {
          context.read<ExemptionRecordsBloc>().add(const LoadExemptionRecordsEvent());
        },
      );
    }

    if (state is ExemptionRecordsEmptyState) {
      return PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: state.message,
      );
    }

    if (state is ExemptionRecordsLoadedState) {
      return _buildSingleRecord(context, state);
    }

    if (state is ExemptionRecordsRefreshingState) {
      return _buildSingleRecordWithRefreshing(context, state.previousRecords);
    }

    return const PageStateWidget(
      state: PageStateType.empty,
    );
  }

  /// 构建单条记录显示
  Widget _buildSingleRecord(BuildContext context, ExemptionRecordsLoadedState state) {
    if (state.records.isEmpty) {
      return const PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: '暂无免实习记录',
      );
    }

    final record = state.records.first;
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        child: _buildRecordCard(context, record),
      ),
    );
  }

  /// 构建带刷新状态的单条记录显示
  Widget _buildSingleRecordWithRefreshing(BuildContext context, List<ExemptionRecordModel> records) {
    if (records.isEmpty) {
      return const PageStateWidget(
        state: PageStateType.empty,
        emptyMessage: '暂无免实习记录',
      );
    }

    final record = records.first;
    return RefreshIndicator(
      onRefresh: () async {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      },
      child: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 25.w, vertical: 20.h),
        child: _buildRecordCard(context, record),
      ),
    );
  }

  /// 构建记录卡片
  Widget _buildRecordCard(BuildContext context, ExemptionRecordModel record) {
    return Container(
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态标题
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: _getStatusColor(record.status).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Text(
                  record.statusText,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: _getStatusColor(record.status),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                record.submitTimeText,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: AppTheme.textHintColor,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // 学生信息
          Row(
            children: [
              CircleAvatar(
                radius: 20.r,
                backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                child: Text(
                  record.studentName.isNotEmpty ? record.studentName[0] : '学',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Text(
                record.studentName,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          SizedBox(height: 16.h),

          // 免实习原因
          _buildInfoRow('免实习原因', record.reason),

          // 证明文件
          if (record.fileUrl != null && record.fileUrl!.isNotEmpty) ...[
            SizedBox(height: 12.h),
            _buildFileRow(record.fileUrl!),
          ],

          // 审核信息
          if (record.reviewOpinion != null && record.reviewOpinion!.isNotEmpty) ...[
            SizedBox(height: 16.h),
            Divider(color: Colors.grey[200]),
            SizedBox(height: 16.h),
            _buildInfoRow('审核人', record.reviewPerson ?? ''),
            SizedBox(height: 8.h),
            _buildInfoRow('审核意见', record.reviewOpinion!),
          ],
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(int status) {
    switch (status) {
      case 0: // 待审批
        return Colors.orange;
      case 1: // 已通过
        return Colors.green;
      case 2: // 驳回
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            '$label：',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textSecondaryColor,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.textPrimaryColor,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建文件行
  Widget _buildFileRow(String fileUrl) {
    return Row(
      children: [
        Icon(
          Icons.attach_file,
          size: 16.sp,
          color: AppTheme.primaryColor,
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            '证明文件',
            style: TextStyle(
              fontSize: 14.sp,
              color: AppTheme.primaryColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        Icon(
          Icons.download,
          size: 16.sp,
          color: AppTheme.primaryColor,
        ),
      ],
    );
  }



  /// 构建右下角浮动按钮
  Widget _buildFloatingActionButton(BuildContext context) {
    return Container(
      width: 100.w,
      height: 100.w,
      margin: EdgeInsets.only(right: 20.w, bottom: 20.h),
      child: FloatingActionButton(
        onPressed: () => _onAddNewApplication(context),
        backgroundColor: const Color(0xFF2165F6),
        elevation: 6,
        shape: const CircleBorder(), // 确保是完美的圆形
        child: Icon(
          Icons.add,
          color: Colors.white,
          size: 40.sp,
        ),
      ),
    );
  }

  /// 处理记录点击事件
  void _onRecordTap(BuildContext context, ExemptionRecordModel record) {
    // TODO(feature): 导航到免实习记录详情页面
    // 这里可以显示详情弹窗或导航到详情页面
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('记录详情'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('申请状态：${record.statusText}'),
            SizedBox(height: 8.h),
            Text('免实习原因：${record.reason}'),
            SizedBox(height: 8.h),
            Text('提交时间：${record.submitTimeText}'),
            if (record.reviewOpinion != null && record.reviewOpinion!.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Text('审核人：${record.reviewPerson ?? ''}'),
              SizedBox(height: 8.h),
              Text('审核意见：${record.reviewOpinion}'),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  /// 处理添加新申请事件
  void _onAddNewApplication(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const StudentExemptionApplicationScreen(),
      ),
    ).then((_) {
      // 返回时刷新列表
      if (context.mounted) {
        context.read<ExemptionRecordsBloc>().add(const RefreshExemptionRecordsEvent());
      }
    });
  }
}
