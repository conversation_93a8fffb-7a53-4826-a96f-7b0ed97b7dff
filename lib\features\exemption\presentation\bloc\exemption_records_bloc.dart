/// -----
/// exemption_records_bloc.dart
///
/// 免实习记录BLoC实现
/// 处理免实习记录相关的业务逻辑
///
/// <AUTHOR>
/// @version 1.0
/// @copyright Copyright © 2025 亿硕教育
/// -----

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_demo/core/utils/logger.dart';
import '../../data/models/exemption_record_model.dart';
import 'exemption_records_event.dart';
import 'exemption_records_state.dart';

/// 免实习记录BLoC
///
/// 处理免实习记录相关的业务逻辑，包括加载、刷新、筛选、搜索等功能
class ExemptionRecordsBloc extends Bloc<ExemptionRecordsEvent, ExemptionRecordsState> {
  static const String _tag = 'ExemptionRecordsBloc';

  ExemptionRecordsBloc() : super(const ExemptionRecordsInitialState()) {
    // 注册事件处理器
    on<LoadExemptionRecordsEvent>(_onLoadExemptionRecords);
    on<RefreshExemptionRecordsEvent>(_onRefreshExemptionRecords);
    on<ViewExemptionRecordDetailEvent>(_onViewExemptionRecordDetail);
    on<DeleteExemptionRecordEvent>(_onDeleteExemptionRecord);
    on<ResubmitExemptionRecordEvent>(_onResubmitExemptionRecord);
    on<FilterExemptionRecordsEvent>(_onFilterExemptionRecords);
    on<SearchExemptionRecordsEvent>(_onSearchExemptionRecords);
    on<ClearSearchEvent>(_onClearSearch);
    on<LoadMoreExemptionRecordsEvent>(_onLoadMoreExemptionRecords);
  }

  /// 处理加载免实习记录事件
  Future<void> _onLoadExemptionRecords(
    LoadExemptionRecordsEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '开始加载免实习记录列表');

    emit(const ExemptionRecordsLoadingState());

    try {
      // 使用固定的实习计划ID（模拟数据）
      final String planId = event.planId ?? '8';

      Logger.info(_tag, '正在加载实习计划 $planId 的免实习记录');

      // 使用模拟数据，不调用接口
      await Future.delayed(const Duration(milliseconds: 800));

      // 获取单条记录数据
      final record = ExemptionRecordModel.getSampleData();

      Logger.info(_tag, '成功加载免实习记录');
      emit(ExemptionRecordsLoadedState(
        records: [record], // 包装为列表以保持兼容性
        currentPlanId: planId,
        totalCount: 1,
      ));
    } catch (e) {
      Logger.error(_tag, '加载免实习记录失败: $e');
      emit(ExemptionRecordsErrorState(
        message: '加载免实习记录失败: ${e.toString()}',
      ));
    }
  }

  /// 处理刷新免实习记录事件
  Future<void> _onRefreshExemptionRecords(
    RefreshExemptionRecordsEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '开始刷新免实习记录列表');

    final currentState = state;
    List<ExemptionRecordModel> previousRecords = [];
    
    if (currentState is ExemptionRecordsLoadedState) {
      previousRecords = currentState.records;
    }

    emit(ExemptionRecordsRefreshingState(
      previousRecords: previousRecords,
      currentPlanId: event.planId ?? '8',
    ));

    // 重新加载数据
    add(LoadExemptionRecordsEvent(planId: event.planId));
  }

  /// 处理查看免实习记录详情事件
  Future<void> _onViewExemptionRecordDetail(
    ViewExemptionRecordDetailEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '查看免实习记录详情: ${event.recordId}');
    
    // TODO: 实现查看详情逻辑
    // 这里可以导航到详情页面或显示详情弹窗
  }

  /// 处理删除免实习记录事件
  Future<void> _onDeleteExemptionRecord(
    DeleteExemptionRecordEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '删除免实习记录: ${event.recordId}');

    final currentState = state;
    if (currentState is! ExemptionRecordsLoadedState) return;

    emit(ExemptionRecordsOperatingState(
      operationType: 'delete',
      recordId: event.recordId,
      currentRecords: currentState.records,
    ));

    try {
      // TODO: 调用实际的删除API
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedRecords = currentState.records
          .where((record) => record.id.toString() != event.recordId)
          .toList();

      emit(ExemptionRecordsOperationSuccessState(
        operationType: 'delete',
        message: '删除成功',
        updatedRecords: updatedRecords,
      ));

      // 更新为加载状态
      emit(currentState.copyWith(records: updatedRecords));
    } catch (e) {
      Logger.error(_tag, '删除免实习记录失败: $e');
      emit(ExemptionRecordsErrorState(
        message: '删除失败: ${e.toString()}',
        previousRecords: currentState.records,
      ));
    }
  }

  /// 处理重新提交免实习申请事件
  Future<void> _onResubmitExemptionRecord(
    ResubmitExemptionRecordEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '重新提交免实习申请: ${event.recordId}');
    
    // TODO: 实现重新提交逻辑
    // 这里可以导航到申请页面并预填充数据
  }

  /// 处理筛选免实习记录事件
  Future<void> _onFilterExemptionRecords(
    FilterExemptionRecordsEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '筛选免实习记录: ${event.statusFilter}');

    final currentState = state;
    if (currentState is! ExemptionRecordsLoadedState) return;

    // TODO: 实现筛选逻辑
    List<ExemptionRecordModel> filteredRecords = currentState.records;
    
    if (event.statusFilter != null) {
      // 根据状态筛选
      filteredRecords = currentState.records.where((record) {
        return record.status == event.statusFilter;
      }).toList();
    }

    emit(currentState.copyWith(
      records: filteredRecords,
      statusFilter: event.statusFilter,
    ));
  }

  /// 处理搜索免实习记录事件
  Future<void> _onSearchExemptionRecords(
    SearchExemptionRecordsEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '搜索免实习记录: ${event.keyword}');

    final currentState = state;
    if (currentState is! ExemptionRecordsLoadedState) return;

    // TODO: 实现搜索逻辑
    final filteredRecords = currentState.records.where((record) {
      return record.studentName.contains(event.keyword) ||
             record.reason.contains(event.keyword);
    }).toList();

    emit(currentState.copyWith(
      records: filteredRecords,
      searchKeyword: event.keyword,
    ));
  }

  /// 处理清除搜索事件
  Future<void> _onClearSearch(
    ClearSearchEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '清除搜索条件');

    // 重新加载数据
    add(const LoadExemptionRecordsEvent());
  }

  /// 处理加载更多免实习记录事件
  Future<void> _onLoadMoreExemptionRecords(
    LoadMoreExemptionRecordsEvent event,
    Emitter<ExemptionRecordsState> emit,
  ) async {
    Logger.info(_tag, '加载更多免实习记录');

    final currentState = state;
    if (currentState is! ExemptionRecordsLoadedState || !currentState.hasMore) {
      return;
    }

    emit(ExemptionRecordsLoadingMoreState(
      currentRecords: currentState.records,
      currentPlanId: currentState.currentPlanId,
    ));

    try {
      // TODO: 调用实际的分页API
      await Future.delayed(const Duration(milliseconds: 500));
      
      // 模拟加载更多数据
      final moreRecords = <ExemptionRecordModel>[];
      
      emit(currentState.copyWith(
        records: [...currentState.records, ...moreRecords],
        currentPage: currentState.currentPage + 1,
        hasMore: false, // 假设没有更多数据了
      ));
    } catch (e) {
      Logger.error(_tag, '加载更多免实习记录失败: $e');
      emit(ExemptionRecordsErrorState(
        message: '加载更多数据失败: ${e.toString()}',
        previousRecords: currentState.records,
      ));
    }
  }


}
